/* Products Section */
.products-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  font-family: 'Arial', sans-serif;
}

.products-header {
  text-align: center;
  margin-bottom: 40px;
}

.products-header h2 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: bold;
}

.products-header p {
  font-size: 1.2rem;
  color: #666;
  margin: 0;
}

/* Controls */
.products-controls {
  margin-bottom: 40px;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 12px 20px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.3s;
}

.search-input:focus {
  border-color: #2196f3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.category-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 2px solid #e0e0e0;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  white-space: nowrap;
}

.filter-btn:hover {
  background-color: #e0e0e0;
  border-color: #bdbdbd;
}

.filter-btn.active {
  background-color: #2196f3;
  color: white;
  border-color: #2196f3;
}

/* Products Container */
.products-container {
  min-height: 400px;
}

.no-products {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  text-align: center;
}

.empty-menu,
.no-results {
  padding: 40px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px dashed #e0e0e0;
}

.empty-menu h3,
.no-results h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.empty-menu p,
.no-results p {
  color: #666;
  margin: 0;
  font-size: 1rem;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.product-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
  border: 1px solid #e0e0e0;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
  height: 200px;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
}

.placeholder-image span {
  font-size: 3rem;
  opacity: 0.5;
}

.product-info {
  padding: 20px;
}

.product-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.product-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-category-tag {
  display: inline-block;
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 15px;
}

.product-price {
  font-size: 1.4rem;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 15px;
}

.product-actions {
  padding: 0 20px 20px 20px;
}

.add-to-cart-btn {
  width: 100%;
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: #1976d2;
  transform: translateY(-2px);
}

.add-to-cart-btn.added {
  background-color: #4caf50;
  cursor: not-allowed;
}

.add-to-cart-btn:disabled {
  opacity: 0.8;
}

/* Products Summary */
.products-summary {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.products-summary p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .products-section {
    padding: 20px 15px;
  }

  .products-header h2 {
    font-size: 2rem;
  }

  .products-header p {
    font-size: 1rem;
  }

  .search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }

  .category-filters {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .filter-btn {
    flex-shrink: 0;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .product-card {
    max-width: 100%;
  }

  .empty-menu,
  .no-results {
    padding: 30px 20px;
  }
}

@media (max-width: 480px) {
  .products-header h2 {
    font-size: 1.8rem;
  }

  .filter-btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .product-name {
    font-size: 1.2rem;
  }

  .product-price {
    font-size: 1.3rem;
  }

  .add-to-cart-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }
}
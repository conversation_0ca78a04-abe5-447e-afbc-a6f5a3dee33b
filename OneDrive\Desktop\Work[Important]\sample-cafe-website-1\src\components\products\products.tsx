'use client';

import React from 'react';
import { useRestaurant } from '../../context/RestaurantContext';
import { Product } from '../../types';
import './products.css';

function Products() {
  const { products, addToCart, settings } = useRestaurant();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: settings.currency
    }).format(amount);
  };

  const handleAddToCart = (product: Product) => {
    addToCart(product, 1);
    alert(`${product.name} added to cart!`);
  };

  const availableProducts = products.filter(p => p.available);

  return (
    <div className="products-container">
      <h2>Our Menu</h2>

      {availableProducts.length === 0 ? (
        <div className="no-products">
          <p>No products available. Please check the admin panel to add products.</p>
        </div>
      ) : (
        <div className="products-grid">
          {availableProducts.map(product => (
            <div key={product.id} className="product-card">
              <div className="product-info">
                <h3>{product.name}</h3>
                <p>{product.description}</p>
                <p className="category">Category: {product.category}</p>
                <p className="price">{formatCurrency(product.price)}</p>
              </div>
              <button
                onClick={() => handleAddToCart(product)}
                className="add-to-cart-btn"
              >
                Add to Cart
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default Products;
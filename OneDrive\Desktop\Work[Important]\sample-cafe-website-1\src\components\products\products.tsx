'use client';

import React, { useState, useMemo } from 'react';
import { useRestaurant } from '../../context/RestaurantContext';
import { Product } from '../../types';
import './products.css';

function Products() {
  const { products, categories, addToCart, settings } = useRestaurant();
  const [selectedCategory, setSelectedCategory] = useState('All Items');
  const [searchTerm, setSearchTerm] = useState('');
  const [addedToCart, setAddedToCart] = useState<string | null>(null);

  // Filter products based on selected category and search term
  const filteredProducts = useMemo(() => {
    return products.filter(product => {
      const matchesCategory = selectedCategory === 'All Items' || product.category === selectedCategory;
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesCategory && matchesSearch && product.available;
    });
  }, [products, selectedCategory, searchTerm]);

  const handleAddToCart = (product: Product) => {
    addToCart(product, 1);
    setAddedToCart(product.id);
    setTimeout(() => setAddedToCart(null), 2000);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: settings.currency
    }).format(amount);
  };

  return (
    <div className="products-section">
      <div className="products-header">
        <h2>Our Tasty Menu</h2>
        <p>Discover our delicious selection of handcrafted meals</p>
      </div>

      <div className="products-controls">
        <div className="search-bar">
          <input
            type="text"
            placeholder="Search for dishes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="category-filters">
          {categories.map(category => (
            <button
              key={category.id}
              className={`filter-btn ${selectedCategory === category.name ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category.name)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      <div className="products-container">
        {filteredProducts.length === 0 ? (
          <div className="no-products">
            {products.length === 0 ? (
              <div className="empty-menu">
                <h3>Menu Coming Soon!</h3>
                <p>Our chef is preparing something amazing for you. Please check back later!</p>
              </div>
            ) : (
              <div className="no-results">
                <h3>No dishes found</h3>
                <p>Try adjusting your search or selecting a different category.</p>
              </div>
            )}
          </div>
        ) : (
          <div className="products-grid">
            {filteredProducts.map(product => (
              <div key={product.id} className="product-card">
                <div className="product-image">
                  {product.image ? (
                    <img src={product.image} alt={product.name} />
                  ) : (
                    <div className="placeholder-image">
                      <span>🍽️</span>
                    </div>
                  )}
                </div>

                <div className="product-info">
                  <h3 className="product-name">{product.name}</h3>
                  <p className="product-description">{product.description}</p>
                  <div className="product-category-tag">
                    {product.category}
                  </div>
                  <div className="product-price">
                    {formatCurrency(product.price)}
                  </div>
                </div>

                <div className="product-actions">
                  <button
                    onClick={() => handleAddToCart(product)}
                    className={`add-to-cart-btn ${addedToCart === product.id ? 'added' : ''}`}
                    disabled={addedToCart === product.id}
                  >
                    {addedToCart === product.id ? '✓ Added!' : 'Add to Cart'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {filteredProducts.length > 0 && (
        <div className="products-summary">
          <p>Showing {filteredProducts.length} of {products.filter(p => p.available).length} available dishes</p>
        </div>
      )}
    </div>
  );
}

export default Products;
import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import Navbar from '../components/navigation/navbar'
import { RestaurantProvider } from '../context/RestaurantContext'

export const metadata: Metadata = {
  title: "Sample Cafe Website",
  description: "Created by prat<PERSON><PERSON> push<PERSON>",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body suppressHydrationWarning>
        <RestaurantProvider>
          <Navbar/>
          {children}
        </RestaurantProvider>
      </body>
    </html>
  );
}

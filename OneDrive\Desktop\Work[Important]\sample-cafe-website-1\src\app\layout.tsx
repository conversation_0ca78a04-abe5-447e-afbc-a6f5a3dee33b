import type { Metadata } from "next";
import "./globals.css";
import Navbar from '../components/navigation/navbar'

export const metadata: Metadata = {
  title: "Sample Cafe Website",
  description: "Created by prat<PERSON><PERSON> push<PERSON>",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  return (
    <html lang="en">
      <body suppressHydrationWarning>
        <Navbar/>
        {children}
      </body>
    </html>
  );
}

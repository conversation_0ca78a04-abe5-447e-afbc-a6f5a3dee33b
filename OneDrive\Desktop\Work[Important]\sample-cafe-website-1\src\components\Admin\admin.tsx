'use client';

import React, { useState } from 'react';
import { useRestaurant } from '../../context/RestaurantContext';
import OrdersManagement from './OrdersManagement';
import ProductManagement from './ProductManagement';
import SettingsPanel from './SettingsPanel';
import LoginForm from './LoginForm';
import './admin.css';

type AdminTab = 'orders' | 'products' | 'settings';

function Admin() {
  const { isAuthenticated, logout } = useRestaurant();
  const [activeTab, setActiveTab] = useState<AdminTab>('orders');

  if (!isAuthenticated) {
    return <LoginForm />;
  }

  return (
    <div className="admin-container">
      <div className="admin-header">
        <h1>Restaurant Admin Dashboard</h1>
        <button onClick={logout} className="logout-btn">
          Logout
        </button>
      </div>

      <div className="admin-tabs">
        <button
          className={`tab-btn ${activeTab === 'orders' ? 'active' : ''}`}
          onClick={() => setActiveTab('orders')}
        >
          Orders Management
        </button>
        <button
          className={`tab-btn ${activeTab === 'products' ? 'active' : ''}`}
          onClick={() => setActiveTab('products')}
        >
          Product Management
        </button>
        <button
          className={`tab-btn ${activeTab === 'settings' ? 'active' : ''}`}
          onClick={() => setActiveTab('settings')}
        >
          Settings
        </button>
      </div>

      <div className="admin-content">
        {activeTab === 'orders' && <OrdersManagement />}
        {activeTab === 'products' && <ProductManagement />}
        {activeTab === 'settings' && <SettingsPanel />}
      </div>
    </div>
  );
}

export default Admin;
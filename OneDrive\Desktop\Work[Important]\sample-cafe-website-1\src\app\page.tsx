'use client';

import React, { useState } from 'react';
import Navigation from '../components/Navigation/Navigation';
import Home from '../components/Home/Home';
import Products from '../components/products/products';
import Cart from '../components/cart/Cart';
import Admin from '../components/Admin/admin';

export default function MainPage() {
  const [currentPage, setCurrentPage] = useState('home');

  const handlePageChange = (page: string) => {
    setCurrentPage(page);
  };

  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'home':
        return <Home onNavigate={handlePageChange} />;
      case 'menu':
        return <Products />;
      case 'cart':
        return <Cart />;
      case 'admin':
        return <Admin />;
      default:
        return <Home onNavigate={handlePageChange} />;
    }
  };

  return (
    <div className="app-container">
      <Navigation currentPage={currentPage} onPageChange={handlePageChange} />
      <main className="main-content">
        {renderCurrentPage()}
      </main>
    </div>
  );
}


/* Navbar Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}

.navbar-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

/* Logo Styles */
.navbar-logo {
  flex-shrink: 0;
}

.navbar-logo #logo {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
  text-decoration: none;
  transition: color 0.3s ease;
}

.navbar-logo #logo:hover {
  color: #e67e22;
}

/* Navigation List Styles */
.navbar-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.navbar-item {
  position: relative;
}

.navbar-item a {
  color: #2c3e50;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  padding: 0.5rem 0;
  transition: color 0.3s ease;
  position: relative;
}

.navbar-item a:hover {
  color: #e67e22;
}

.navbar-item a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #e67e22;
  transition: width 0.3s ease;
}

.navbar-item a:hover::after {
  width: 100%;
}

/* CTA Button Styles */
.navbar-cta {
  flex-shrink: 0;
}

.navbar-cta a {
  background-color: #e67e22;
  color: white;
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  border: 2px solid #e67e22;
}

.navbar-cta a:hover {
  background-color: transparent;
  color: #e67e22;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(230, 126, 34, 0.3);
}

/* Mobile Toggle Styles */
.mobile-navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 5px;
}

.mobile-navbar-toggle .bar {
  width: 25px;
  height: 3px;
  background-color: #2c3e50;
  margin: 3px 0;
  transition: 0.3s;
  border-radius: 2px;
}

.mobile-navbar-toggle:hover .bar {
  background-color: #e67e22;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .navbar-container {
    padding: 0 15px;
    height: 60px;
  }

  .navbar-logo #logo {
    font-size: 1.5rem;
  }

  .navbar-list {
    position: fixed;
    top: 60px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 60px);
    background-color: #ffffff;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    padding-top: 2rem;
    gap: 1.5rem;
    transition: left 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .navbar-list.active {
    left: 0;
  }

  .navbar-item a {
    font-size: 1.1rem;
    padding: 1rem 0;
  }

  .navbar-cta {
    display: none;
  }

  .mobile-navbar-toggle {
    display: flex;
  }

  /* Mobile toggle animation */
  .mobile-navbar-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
  }

  .mobile-navbar-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .mobile-navbar-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
  }
}

@media screen and (max-width: 480px) {
  .navbar-container {
    padding: 0 10px;
  }

  .navbar-logo #logo {
    font-size: 1.3rem;
  }
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Add padding to body to account for fixed navbar */
body {
  padding-top: 70px;
}

@media screen and (max-width: 768px) {
  body {
    padding-top: 60px;
  }
}
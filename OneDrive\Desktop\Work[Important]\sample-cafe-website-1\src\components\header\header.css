:root {
    --primary-100: #ff9f1c;
    --primary-200: #ffb54f;
    --primary-300: #ffca82;
    --accent-100: #6687a9;
    --accent-200: #6687a9;
    --accent-300: #6687a9;
    --background-100: #fffdfa;
    --background-200: #fff3e1;
    --background-300: #ffe9c7;
    --text-100: #281804;
    --text-200: #563409;
    --text-300: #85500d;
    --white: #ffffff;
    --shadow: rgba(40, 24, 4, 0.1);
    --shadow-hover: rgba(40, 24, 4, 0.2);
    --gradient-primary: linear-gradient(135deg, var(--primary-100), var(--primary-200));
    --gradient-background: linear-gradient(135deg, var(--background-100), var(--background-200));
}

body {
    background: var(--gradient-background);
    margin: 0;
    padding: 0;
    font-family: 'Georgia', 'Times New Roman', serif;
}

/* Header Container */
.tittle {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem;
    background: var(--gradient-background);
    overflow: hidden;
}

.tittle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, var(--primary-300) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, var(--accent-100) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, var(--background-300) 0%, transparent 50%);
    opacity: 0.3;
    z-index: 1;
}

/* Typography Styles */
.tittle h1 {
    position: relative;
    z-index: 2;
    margin: 0;
    padding: 0;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-weight: bold;
    line-height: 0.9;
    text-align: center;
    letter-spacing: -0.02em;
}

/* Individual Title Styling */
#tittle-1 {
    font-size: clamp(3rem, 8vw, 8rem);
    color: var(--text-100);
    text-shadow:
        2px 2px 4px var(--shadow),
        0 0 20px rgba(255, 159, 28, 0.3);
    animation: fadeInUp 1s ease-out;
}

#tittle-1:nth-of-type(2) {
    position: absolute;
    color: transparent;
    -webkit-text-stroke: 2px var(--primary-100);
    text-stroke: 2px var(--primary-100);
    z-index: 1;
    animation: fadeInUp 1s ease-out 0.2s both;
    transform: translate(3px, 3px);
}

#tittle-2 {
    font-size: clamp(4rem, 10vw, 10rem);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: none;
    margin-top: -1rem;
    animation: fadeInUp 1s ease-out 0.4s both;
    position: relative;
}

#tittle-2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
    box-shadow: 0 2px 8px var(--shadow);
}

#tittle-3 {
    font-size: clamp(2.5rem, 6vw, 6rem);
    color: var(--accent-100);
    text-shadow:
        1px 1px 2px var(--shadow),
        0 0 15px rgba(102, 135, 169, 0.4);
    margin-top: -0.5rem;
    animation: fadeInUp 1s ease-out 0.6s both;
    font-style: italic;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Hover Effects */
.tittle:hover #tittle-1 {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.tittle:hover #tittle-2 {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.tittle:hover #tittle-3 {
    transform: scale(1.03) rotate(-1deg);
    transition: transform 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tittle {
        min-height: 80vh;
        padding: 1rem;
    }

    #tittle-1 {
        font-size: clamp(2rem, 6vw, 4rem);
    }

    #tittle-2 {
        font-size: clamp(2.5rem, 7vw, 5rem);
        margin-top: -0.5rem;
    }

    #tittle-3 {
        font-size: clamp(1.5rem, 4vw, 3rem);
        margin-top: -0.3rem;
    }

    #tittle-1:nth-of-type(2) {
        transform: translate(2px, 2px);
    }
}

@media (max-width: 480px) {
    .tittle {
        min-height: 70vh;
        padding: 0.5rem;
    }

    .tittle h1 {
        letter-spacing: -0.01em;
    }

    #tittle-2::after {
        height: 2px;
        bottom: -5px;
    }
}
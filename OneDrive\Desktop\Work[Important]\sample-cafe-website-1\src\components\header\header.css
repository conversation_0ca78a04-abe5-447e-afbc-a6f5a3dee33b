:root {
    --primary-orange: #ff9f1c;
    --primary-orange-light: #ffb54f;
    --text-black: #000000;
    --text-gray: #666666;
    --background-cream: #fff8f0;
    --white: #ffffff;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.2);
}

body {
    background: var(--background-cream);
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
}

/* Header Container */
.header {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    min-height: 100vh;
    padding: 2rem 3rem;
    background: var(--background-cream);
    overflow: hidden;
}

.header-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 600px;
    margin-top: 5rem;
}

.header-image {
    flex: 1;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 500px;
    height: 500px;
    margin-left: 2rem;
}

#header-image {
    object-fit: contain;
    border-radius: 50%;
    filter: drop-shadow(0 10px 30px rgba(255, 159, 28, 0.3));
    transition: transform 0.3s ease;
    max-width: 100%;
    height: auto;
}

#header-image:hover {
    transform: scale(1.05);
}

/* Title Section */
.title-section {
    margin-bottom: 2rem;
}

.title-section h1 {
    margin: 0;
    padding: 0;
    font-family: 'Arial', sans-serif;
    font-weight: 900;
    line-height: 0.9;
    letter-spacing: -0.02em;
}

/* Individual Title Lines */
.title-line-1 {
    font-size: clamp(3rem, 8vw, 5rem);
    color: var(--text-black);
    margin-bottom: -0.2rem;
}

.title-line-2 {
    font-size: clamp(3rem, 8vw, 5rem);
    color: var(--primary-orange);
    margin-bottom: -0.2rem;
}

.title-line-3 {
    font-size: clamp(3rem, 8vw, 5rem);
    color: var(--primary-orange);
    margin-bottom: -0.2rem;
}

.title-line-4 {
    font-size: clamp(3rem, 8vw, 5rem);
    color: var(--primary-orange);
}

/* Description */
.description {
    margin-bottom: 3rem;
}

.description p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-gray);
    margin: 0;
    max-width: 500px;
}

/* Button Section */
.button-section {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn-primary {
    background-color: var(--primary-orange);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(255, 159, 28, 0.3);
}

.btn-primary:hover {
    background-color: var(--primary-orange-light);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 159, 28, 0.4);
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-orange);
    border: 2px solid var(--primary-orange);
    padding: 10px 22px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--primary-orange);
    color: var(--white);
    transform: translateY(-2px);
}


/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--text-gray);
    font-size: 0.9rem;
}

.scroll-arrow {
    margin-top: 0.5rem;
    font-size: 1.2rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: row;
        padding: 1rem;
        min-height: 90vh;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-content {
        margin-top: 5rem;
        max-width: 60%;
        flex: 1;
    }

    .header-image {
        margin-left: 5rem;
        margin-top: 2rem;
        height: auto;
        max-width: 35%;
        width: 35%;
        min-width: 150px;
        display: flex !important;
        justify-content: center;
        align-items: flex-start;
    }

    .title-section h1 {
        font-size: clamp(1.8rem, 5vw, 2.5rem) !important;
        text-align: left;
    }

    .description p {
        font-size: 0.9rem;
        text-align: left;
    }

    .button-section {
        flex-direction: row;
        align-items: flex-start;
        gap: 0.8rem;
    }

    .btn-primary,
    .btn-secondary {
        width: 180px;
        text-align: center;
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    #header-image {
        width: 100% !important;
        height: auto !important;
        max-width: 100%;
        min-width: 150px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 0.8rem;
        gap: 0.8rem;
    }

    .header-content {
        margin-top: 5rem;
        max-width: 65%;
    }

    .header-image {
        height: auto;
        max-width: 30%;
        width: 30%;
        min-width: 120px;
        margin-top: 5rem;
        margin-left: 0rem;
        display: flex !important;
        justify-content: center;
        align-items: flex-start;
    }

    .title-section h1 {
        font-size: clamp(1.5rem, 4.5vw, 2rem) !important;
        line-height: 1.1;
        margin-bottom: -0.1rem;
    }

    .description p {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .button-section {
        flex-direction: row;
        gap: 1rem;
    }

    .btn-primary,
    .btn-secondary {
        width: 140px;
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    #header-image {
        width: 100% !important;
        height: auto !important;
        max-width: 100%;
        min-width: 120px;
    }

    .scroll-indicator {
        bottom: 15rem;
    }
}

/* Very small screens */
@media (max-width: 360px) {
    .header {
        flex-direction: column;
        padding: 0.5rem;
        text-align: center;
    }

    .header-content {
        max-width: 100%;
        margin-top: 5rem;
    }

    .header-image {
        max-width: 200px;
        width: 200px;
        margin: 1rem auto 0;
        align-self: center;
    }

    .title-section h1 {
        font-size: clamp(1.3rem, 4vw, 1.8rem) !important;
        text-align: center;
    }

    .description p {
        text-align: center;
        font-size: 0.75rem;
    }

    .button-section {
        flex-direction: row;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary,
    .btn-secondary {
        width: 110px;
        padding: 8px 12px;
        font-size: 0.75rem;
    }

    #header-image {
        width: 100% !important;
        height: auto !important;
        max-width: 200px;
    }
}
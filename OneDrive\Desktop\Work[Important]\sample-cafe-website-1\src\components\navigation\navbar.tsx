"use client";

import React, { useState } from 'react'
import './navbar.css'

function Navbar() {
    const [isMenuOpen, setIsMenuOpen] = useState(false)

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen)
    }

    const closeMenu = () => {
        setIsMenuOpen(false)
    }

    return (
        <div className='navbar'>
            <div className='navbar-container'>
                <div className='navbar-logo'>
                    <a id='logo' href='#'>Brand Name</a>
                </div>
                <ul className={`navbar-list ${isMenuOpen ? 'active' : ''}`}>
                    <li className='navbar-item'><a href='#' onClick={closeMenu}>Home</a></li>
                    <li className='navbar-item'><a href='#' onClick={closeMenu}>About</a></li>
                    <li className='navbar-item'><a href='#' onClick={closeMenu}>Contact</a></li>
                    <li className='navbar-item'><a href='#' onClick={closeMenu}>Cart</a></li>
                </ul>
                <div className='navbar-cta'>
                    <a href='#'>Get Started</a>
                </div>
                <div
                    className={`mobile-navbar-toggle ${isMenuOpen ? 'active' : ''}`}
                    onClick={toggleMenu}
                >
                    <span className="bar"></span>
                    <span className="bar"></span>
                    <span className="bar"></span>
                </div>
            </div>
        </div>
    )
}

export default Navbar
